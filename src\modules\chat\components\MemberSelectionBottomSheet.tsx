import React, { useState, useEffect, forwardRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { ColorThemes } from '../../../assets/skin/colors';
import { hideBottomSheet, Winicon } from 'wini-mobile-components';
import Chat<PERSON><PERSON> from '../services/ChatAPI';
import FastImage from 'react-native-fast-image';
import Config<PERSON><PERSON> from '../../../Config/ConfigAPI';

interface User {
  Id: string;
  Name: string;
  AvatarUrl?: string;
  Email?: string;
  isSelected?: boolean;
}

interface MemberSelectionBottomSheetProps {
  onSelectMembers: (members: User[]) => void;
  selectedMembers: User[];
}

const MemberSelectionBottomSheet = forwardRef<any, MemberSelectionBottomSheetProps>(
  ({ onSelectMembers, selectedMembers }, ref) => {
    const [searchQuery, setSearchQuery] = useState('');
    const [searchResults, setSearchResults] = useState<User[]>([]);
    const [isSearching, setIsSearching] = useState(false);
    // const [tempSelectedMembers, setTempSelectedMembers] = useState<User[]>(selectedMembers);

    useEffect(() => {
      if (searchQuery.trim().length > 2) {
        searchUsers();
      } else {
        setSearchResults([]);
      }
    }, [searchQuery]);

    const searchUsers = async () => {
      try {
        setIsSearching(true);
        const results = await ChatAPI.searchUsers(searchQuery);
        setSearchResults(results);
      } catch (error) {
        console.error('Error searching users:', error);
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    };
    const loadData = async () => {
      try {
        setIsSearching(true);
        const results = await ChatAPI.searchUsers();
        debugger
        setSearchResults(results);
      } catch (error) {
        console.error('Error searching users:', error);
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    };
    useEffect(() => {
      loadData();
    }, []);

    const toggleUserSelection = (user: User) => {
      setSearchResults((prev) =>
        prev.map((u) => (u.Id === user.Id ? { ...u, isSelected: !u.isSelected } : u))
      );
    };

    const handleConfirm = () => {
      onSelectMembers(searchResults.filter((u) => u.isSelected));
      if (ref) {
        hideBottomSheet(ref as any);
      }
    };

    const handleCancel = () => {
      if (ref) {
        hideBottomSheet(ref as any);
      }
    };
    //random color
    const getRandomAvatarColor = (name: string) => {
      const colors = [
        '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
        '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
      ];
      const index = name?.charCodeAt(0) % colors.length;
      return colors[index];
    };

    const renderSearchResult = ({ item }: { item: User }) => {
      // const isSelected = tempSelectedMembers.find(u => u.id === item.id);
      
      return (
        <TouchableOpacity
          style={[styles.userItem]}
          onPress={() => toggleUserSelection(item)}
          activeOpacity={0.7}
        >
          {item.AvatarUrl ? (
            <FastImage 
              source={{ uri: item.AvatarUrl.includes('http') ? item.AvatarUrl : ConfigAPI.urlImg + item.AvatarUrl }} 
              style={styles.userAvatar} 
            />
          ) : (
            <View style={[styles.userAvatar, styles.defaultAvatar, { backgroundColor: getRandomAvatarColor(item.Name) }]}>
              <Text style={styles.avatarText}>
                {item.Name?.charAt(0).toUpperCase()}
              </Text>
            </View>
          )}
          
          <View style={styles.userInfo}>
            <Text style={styles.userName}>{item.Name}</Text>
            {item.Email && (
              <Text style={styles.userEmail}>{item.Email}</Text>
            )}
          </View>
          
          <View style={[styles.checkbox]}>
            {item.isSelected && <Text style={styles.checkmark}>✓</Text>}
          </View>
        </TouchableOpacity>
      );
    };

    const renderSelectedMember = ({ item }: { item: User }) => (
      <View style={styles.selectedMemberChip}>
        {item.AvatarUrl ? (
          <FastImage 
            source={{ uri: item.AvatarUrl.includes('http') ? item.AvatarUrl : ConfigAPI.urlImg + item.AvatarUrl }} 
            style={styles.chipAvatar} 
          />
        ) : (
          <View style={[styles.chipAvatar, styles.defaultAvatar , { backgroundColor: getRandomAvatarColor(item.Name) }]}>
            <Text style={styles.chipAvatarText}>
              {item.Name?.charAt(0).toUpperCase()}
            </Text>
          </View>
        )}
        <Text style={styles.chipName} numberOfLines={1}>
          {item.Name}
        </Text>
        <TouchableOpacity
          style={styles.chipRemoveButton}
          onPress={() => toggleUserSelection(item)}
        >
          <Text style={styles.chipRemoveText}>×</Text>
        </TouchableOpacity>
      </View>
    );

    return (
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleCancel}>
            <Text style={styles.cancelButton}>Hủy</Text>
          </TouchableOpacity>
          <Text style={styles.title}>Chọn thành viên</Text>
          <TouchableOpacity onPress={handleConfirm}>
            <Text style={styles.confirmButton}>Xong</Text>
          </TouchableOpacity>
        </View>

        {/* Selected Members */}
        {searchResults.filter((u) => u.isSelected).length > 0 && (
          <View style={styles.selectedSection}>
            <Text style={styles.selectedTitle}>
              Đã chọn ({searchResults.filter((u) => u.isSelected).length})
            </Text>
            <FlatList
              data={searchResults.filter((u) => u.isSelected)}
              renderItem={renderSelectedMember}
              keyExtractor={(item) => item.Id}
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.selectedList}
            />
          </View>
        )}

        {/* Search Input */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Winicon
              src="outline/user interface/search"
              size={16}
              color={ColorThemes.light.neutral_text_secondary_color}
            />
            <TextInput
              style={styles.searchInput}
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder="Tìm kiếm người dùng..."
              placeholderTextColor={ColorThemes.light.neutral_text_subtitle_color}
            />
          </View>
        </View>

        {/* Search Results */}
        <FlatList
          data={searchResults}
          renderItem={renderSearchResult}
          keyExtractor={(item) => item.Id}
          style={styles.searchResults}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            searchQuery.length > 2 && !isSearching ? (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>Không tìm thấy người dùng</Text>
              </View>
            ) : searchQuery.length <= 2 ? (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>Nhập ít nhất 3 ký tự để tìm kiếm</Text>
              </View>
            ) : null
          }
          ListFooterComponent={
            isSearching ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color={ColorThemes.light.primary_color} />
                <Text style={styles.loadingText}>Đang tìm kiếm...</Text>
              </View>
            ) : null
          }
        />
      </View>
    );
  }
);

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    maxHeight: '80%',
    minHeight: 600,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    width: '100%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_border_color,
  },
  cancelButton: {
    color: ColorThemes.light.error_color,
    fontSize: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: ColorThemes.light.neutral_text_color,
  },
  confirmButton: {
    color: ColorThemes.light.primary_color,
    fontSize: 16,
    fontWeight: '600',
  },
  selectedSection: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_border_color,
  },
  selectedTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: ColorThemes.light.neutral_text_color,
    marginBottom: 8,
  },
  selectedList: {
    marginTop: 8,
  },
  selectedMemberChip: {
    flexDirection: 'column',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.neutral_background_color,
    borderRadius: 20,
    paddingHorizontal: 8,
    // paddingVertical: 4,
    marginRight: 8,
  },
  chipAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 6,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_border_color,
  },
  chipAvatarText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  chipName: {
    fontSize: 12,
    color: ColorThemes.light.neutral_border_color,
  },
  chipRemoveButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    position:'absolute',
    top: -2,
    right: -2,
  },
  chipRemoveText: {
    color: ColorThemes.light.neutral_border_color,
    fontSize: 14,
    fontWeight: 'bold',
  },
  // searchContainer: {
  //   padding: 16,
  //   borderBottomWidth: 1,
  //   borderBottomColor: ColorThemes.light.neutral_border_color,
  // },
  // searchInputContainer: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   borderWidth: 1,
  //   borderColor: ColorThemes.light.neutral_border_color,
  //   borderRadius: 8,
  //   paddingHorizontal: 12,
  //   paddingVertical: 8,
  // },
  // searchInput: {
  //   flex: 1,
  //   marginLeft: 8,
  //   fontSize: 16,
  //   color: ColorThemes.light.neutral_text_color,
  // },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.neutral_main_background_color,
    borderRadius: 8,
    paddingHorizontal: 12,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 14,
    color: ColorThemes.light.neutral_text_title_color,
  },
  searchResults: {
    flex: 1,
  },
  userItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_border_color,
  },
  selectedUserItem: {
    backgroundColor: ColorThemes.light.primary_main_color,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  defaultAvatar: {
    backgroundColor: ColorThemes.light.primary_color,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '500',
    color: ColorThemes.light.neutral_text_color,
  },
  userEmail: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_secondary_color,
    marginTop: 2,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmark: {
    color: ColorThemes.light.primary_main_color,
    fontSize: 16,
    fontWeight: 'bold',
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: ColorThemes.light.neutral_text_secondary_color,
  },
  loadingContainer: {
    padding: 16,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_secondary_color,
    marginTop: 8,
  },
});

export default MemberSelectionBottomSheet;
