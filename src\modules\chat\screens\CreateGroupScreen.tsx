import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  FlatList,
  Image,
  Alert,
} from 'react-native';
import { useDispatch } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import { ColorThemes } from '../../../assets/skin/colors';
import { createGroupRoom } from '../../../redux/reducers/ChatReducer';
import { useChatLoading } from '../../../redux/hook/chatHook';
import ChatAPI from '../services/ChatAPI';
import { ComponentStatus, showSnackbar, FBottomSheet, showBottomSheet } from 'wini-mobile-components';
import FastImage from 'react-native-fast-image';
import { navigate, RootScreen } from '../../../router/router';
import { InforHeader } from '../../../Screen/Layout/headers/inforHeader';
import ConfigAPI from '../../../Config/ConfigAPI';
import MemberSelectionBottomSheet from '../components/MemberSelectionBottomSheet';

interface User {
  id: string;
  Name: string;
  Avatar?: string;
  Email?: string;
}

const CreateGroupScreen: React.FC = () => {
  const dispatch = useDispatch<any>();
  const navigation = useNavigation();
  const loading = useChatLoading();
  const bottomSheetRef = useRef<any>(null);

  const [groupName, setGroupName] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);

  const handleSelectMembers = (members: User[]) => {
    setSelectedUsers(members);
  };

  const showMemberSelection = () => {
    showBottomSheet({
      ref: bottomSheetRef,
      // enableDismiss: true,
      children: (
        <MemberSelectionBottomSheet
          ref={bottomSheetRef}
          onSelectMembers={handleSelectMembers}
          selectedMembers={selectedUsers}
        />
      ),
    });
  };

  const createGroup = async () => {
    if (!groupName.trim()) {
      showSnackbar({
        status: ComponentStatus.WARNING,
        message: 'Vui lòng nhập tên nhóm',
      });
      return;
    }

    if (selectedUsers.length < 2) {
      showSnackbar({
        status: ComponentStatus.WARNING,
        message: 'Vui lòng chọn ít nhất 2 thành viên',
      });
      return;
    }

    try {
      const groupData = {
        name: groupName.trim(),
        participants: selectedUsers.map(user => user.id),
      };

      const newRoom = await dispatch(createGroupRoom(groupData));
      
      showSnackbar({
        status: ComponentStatus.SUCCSESS,
        message: 'Tạo nhóm thành công',
      });

      // Navigate to the new chat room
      navigate(RootScreen.ChatRoom, { room: newRoom });
      
    } catch (error) {
      console.error('Error creating group:', error);
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: 'Không thể tạo nhóm chat',
      });
    }
  };

  const renderSelectedUser = ({ item }: { item: User }) => (
    <View style={styles.selectedUserItem}>
      {item.Avatar ? (
        <FastImage source={{ uri: ConfigAPI.urlImg + item.Avatar }} style={styles.selectedUserAvatar} />
      ) : (
        <View style={[styles.selectedUserAvatar, styles.defaultAvatar]}>
          <Text style={styles.avatarText}>
            {item.Name?.charAt(0).toUpperCase()}
          </Text>
        </View>
      )}
      <Text style={styles.selectedUserName} numberOfLines={1}>
        {item.Name}
      </Text>
      <TouchableOpacity
        style={styles.removeButton}
        onPress={() => {
          setSelectedUsers(selectedUsers.filter(u => u.id !== item.id));
        }}
      >
        <Text style={styles.removeButtonText}>×</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <InforHeader title={'Tạo nhóm chát'}
      customActions={
        <TouchableOpacity
          onPress={createGroup}
          disabled={loading || !groupName.trim() || selectedUsers.length < 2}
        >
          <Text style={[
            styles.createButton,
            (loading || !groupName.trim() || selectedUsers.length < 2) && styles.disabledButton,
            {
              marginRight: 16,
            }
          ]}>
            Tạo
          </Text>
        </TouchableOpacity>

      }
      showAction={true}
      />

      {/* Group Name Input */}
      <View style={styles.groupNameSection}>
        <Text style={styles.sectionTitle}>Tên nhóm</Text>
        <TextInput
          style={styles.groupNameInput}
          value={groupName}
          onChangeText={setGroupName}
          placeholder="Nhập tên nhóm..."
          maxLength={50}
        />
      </View>

      {/* Selected Users */}
      {selectedUsers.length > 0 && (
        <View style={styles.selectedUsersSection}>
          <Text style={styles.sectionTitle}>
            Đã chọn ({selectedUsers.length})
          </Text>
          <FlatList
            data={selectedUsers}
            renderItem={renderSelectedUser}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.selectedUsersList}
          />
        </View>
      )}

      {/* Add Members Button */}
      <View style={styles.addMembersSection}>
        <Text style={styles.sectionTitle}>Thêm thành viên</Text>
        <TouchableOpacity
          style={styles.addMembersButton}
          onPress={showMemberSelection}
          activeOpacity={0.7}
        >
          <Text style={styles.addMembersButtonText}>
            {selectedUsers.length > 0
              ? `Đã chọn ${selectedUsers.length} thành viên`
              : 'Chọn thành viên cho nhóm'
            }
          </Text>
          <Text style={styles.addMembersArrow}>›</Text>
        </TouchableOpacity>
      </View>

      {/* Bottom Sheet */}
      <FBottomSheet ref={bottomSheetRef} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_border_color,
  },
  cancelButton: {
    color: ColorThemes.light.error_color,
    fontSize: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: ColorThemes.light.neutral_text_color,
  },
  createButton: {
    color: ColorThemes.light.primary_color,
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButton: {
    color: ColorThemes.light.neutral_text_secondary_color,
  },
  groupNameSection: {
    backgroundColor: 'white',
    padding: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: ColorThemes.light.neutral_text_color,
    marginBottom: 8,
  },
  groupNameInput: {
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_border_color,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
  },
  selectedUsersSection: {
    backgroundColor: 'white',
    padding: 16,
    marginBottom: 8,
  },
  selectedUsersList: {
    marginTop: 8,
  },
  selectedUserItem: {
    alignItems: 'center',
    marginRight: 12,
    width: 60,
  },
  selectedUserAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginBottom: 4,
  },
  selectedUserName: {
    fontSize: 12,
    color: ColorThemes.light.neutral_text_color,
    textAlign: 'center',
  },
  removeButton: {
    position: 'absolute',
    top: -5,
    right: -5,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: ColorThemes.light.error_color,
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  addMembersSection: {
    backgroundColor: 'white',
    padding: 16,
    marginBottom: 8,
  },
  addMembersButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_border_color,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    backgroundColor: ColorThemes.light.neutral_background_color,
  },
  addMembersButtonText: {
    fontSize: 16,
    color: ColorThemes.light.neutral_text_secondary_color,
    flex: 1,
  },
  addMembersArrow: {
    fontSize: 18,
    color: ColorThemes.light.neutral_text_secondary_color,
    fontWeight: 'bold',
  },
  defaultAvatar: {
    backgroundColor: ColorThemes.light.primary_color,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default CreateGroupScreen;
